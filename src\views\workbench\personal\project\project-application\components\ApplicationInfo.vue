<template>
  <div class="flex h-full flex-col">
    <div class="h-0 flex-1">
      <el-scrollbar height="100%">
        <div class="content-item">
          <div class="item-title">
            <h4>申请信息</h4>
            <!-- <div class="cursor-pointer text-sm text-p" @click="onHelp">
              <i class="iconfont icon-service mr-2" style="font-size: 14px" />在线帮助
            </div> -->
          </div>

          <el-form
            ref="appFormRef"
            v-loading="loading"
            :model="appForm"
            :rules="appRules"
            label-position="top"
            class="mt-4 px-10"
            hide-required-asterisk
            :disabled="readonly"
          >
            <el-form-item label="项目名称：" prop="title">
              <el-input v-model="appForm.title" placeholder="请输入项目名称" maxlength="200" />
            </el-form-item>

            <el-form-item label="项目问题和目标：" prop="questionObjective">
              <el-input
                v-model="appForm.questionObjective"
                placeholder="请输入项目问题和目标"
                type="textarea"
                :rows="4"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="项目背景与科学原理：" prop="backgroundPrinciple">
              <el-input
                v-model="appForm.backgroundPrinciple"
                placeholder="请输入项目背景与科学原理"
                type="textarea"
                :rows="4"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="使用方法说明：" prop="methodDescription">
              <el-input
                v-model="appForm.methodDescription"
                placeholder="请输入使用方法说明"
                type="textarea"
                :rows="4"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="所需数据集的类型和大小：" prop="datasetTypeVolume">
              <el-input
                v-model="appForm.datasetTypeVolume"
                placeholder="请输入所需数据集的类型和大小"
                type="textarea"
                :rows="4"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="研究的预期价值：" prop="researchValue">
              <el-input
                v-model="appForm.researchValue"
                placeholder="请输入研究的预期价值"
                type="textarea"
                :rows="4"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="项目关键词：" prop="keyword">
              <TagGroup v-model="appForm.keyword" :readonly="readonly" />
            </el-form-item>

            <el-form-item label="简述项目摘要，说明目标、科学原理、项目持续时间和公共卫生影响：" prop="summary">
              <el-input
                v-model="appForm.summary"
                placeholder="请输入简述"
                maxlength="500"
                type="textarea"
                :rows="5"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="项目是否会产生从现有复杂数据集衍生的任何新数据字段：">
              <div class="w-full">
                <el-radio-group v-model="appForm.haveDerivant">
                  <el-radio :value="true"> 是 </el-radio>
                  <el-radio :value="false"> 否 </el-radio>
                </el-radio-group>

                <!-- <div v-if="appForm.haveDerivant" class="w-full bg-baf p-4">
                  <el-form-item label="请概述您打算生成的变量：" prop="newDataIntro">
                    <el-input
                      v-model="appForm.newDataIntro"
                      placeholder="请输入概述"
                      type="textarea"
                      :rows="5"
                      show-word-limit
                    />
                  </el-form-item>
                </div> -->
              </div>
            </el-form-item>

            <el-form-item label="项目预计持续时间：" prop="duration">
              <el-input-number v-model="appForm.duration" :min="1" :max="999" step-strictly />
              <span class="ml-2">个月</span>
            </el-form-item>

            <!-- <el-form-item label="附件：" prop="attachment">
              <UploadButton v-model="appForm.attachment" />
            </el-form-item> -->
          </el-form>

          <div class="bg-baf text-regular m-5 mt-0 p-4 text-sm">
            <p>请注意，您应该发布或公开您的结果并返回国家脑疾病临床大数据平台：</p>
            <ul class="list-disc pl-5">
              <li>任何重要的派生变量；</li>
              <li>用于生成它们的方法的描述；</li>
              <li>用于生成论文主要结果的底层语法/代码；</li>
              <li>一个简短的外行描述，总结发现。</li>
            </ul>
            <p>
              这些应在每次出版后的6个月内或项目结束日期后的12个月内提供（以先到者为准）。您在出版前至少提前两周向我们发送一份您已接受的手稿副本，并提醒我们是否存在与调查结果相关的任何道德或争议问题。
            </p>
          </div>
        </div>

        <!-- <div class="content-item pb-5">
          <div class="item-title">
            <h4>数据字段选择</h4>
            <div class="cursor-pointer text-sm text-p" @click="onHelp">
              <i class="iconfont icon-service mr-2" style="font-size: 14px"></i>在线帮助
            </div>
          </div>

          <div class="mt-4 flex items-center px-10">
            <el-button v-if="!readonly" type="primary" class="mr-5" @click="onAddOrder">创建数据订单</el-button>
            <div class="text text-sm text-regular">
              总计: {{ totalNum }}个标准字段 ({{ tableTable }}个表格、{{ snpNum }}个遗传SNP、{{ dataNum }}个数据集) 和
              {{ batchFieldNum }}个批量字段 ({{ batchNum }}个批量、{{ hesNum }}个HES记录)
            </div>
          </div>

          <div class="mt-4 px-10">
            <DataField v-if="orderTable.length" v-model="orderTable" :readonly="readonly" />
            <el-empty v-else description="暂无数据订单" />
          </div>
        </div> -->

        <div class="h-5" />
      </el-scrollbar>
    </div>

    <div v-if="!readonly" class="action-footer">
      <el-button :loading="saveLoading" type="primary" @click="onSubmit"> 确定 </el-button>
      <!-- <el-button @click="onSaveTemp">保存草稿</el-button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  /* 申请信息 */
  // import UploadButton from '@/components/UploadButton.vue';
  // import DataField from './DataField.vue';
  import { findAppById, newOrUpdateApplication_02, newOrUpdateApplication_1 } from '@/api/index';
  import TagGroup from '@/components/TagGroup.vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { useRouter } from 'vue-router';
  import { useUsers } from '@/store/index';
  const router = useRouter();
  const emit = defineEmits<{ getState: [string] }>();
  const store = useUsers();

  const props = defineProps({
    id: {
      type: String,
    },
  });

  const readonly = ref(false);
  const loading = ref(false);
  const appFormRef = ref();
  const appForm = reactive({
    title: '',
    questionObjective: '',
    backgroundPrinciple: '',
    methodDescription: '',
    datasetTypeVolume: '',
    researchValue: '',
    keyword: '',
    summary: '',
    haveDerivant: true,
    // newDataIntro: '',
    duration: 1,
    // attachment: [],
  });
  const appRules = reactive({
    title: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    questionObjective: [{ required: true, message: '请输入项目问题和目标', trigger: 'blur' }],
    backgroundPrinciple: [{ required: true, message: '请输入项目背景与科学原理', trigger: 'blur' }],
    methodDescription: [{ required: true, message: '请输入使用方法说明', trigger: 'blur' }],
    datasetTypeVolume: [{ required: true, message: '请输入所需数据集的类型和大小', trigger: 'blur' }],
    researchValue: [{ required: true, message: '请输入研究的预期价值', trigger: 'blur' }],
    keyword: [{ required: true, message: '请输入项目关键词', trigger: 'blur' }],
    summary: [{ required: true, message: '请输入简述', trigger: 'blur' }],
    // newDataIntro: [{ required: true, message: '请输入概述', trigger: 'blur' }],
    duration: [{ required: true, message: '请选择项目预计持续时间', trigger: 'blur' }],
    // attachment: [{ required: true, message: '请上传附件', trigger: 'blur' }],
  });
  //提交
  const saveLoading = ref(false);
  const onSubmit = async () => {
    appFormRef.value.validate((valid) => {
      if (valid) {
        ElMessageBox.alert(`确定${props.id ? '修改' : '创建'}项目？`, {
          showCancelButton: true,
          type: 'warning',
          callback: async (action) => {
            if (action === 'confirm') {
              try {
                saveLoading.value = true;
                const params = {};
                Object.keys(appForm).forEach((key) => {
                  if (key !== 'keyword') {
                    params[key] = appForm[key];
                  }
                });
                appForm.keyword.split(',').forEach((item, i) => {
                  params[`keyWord${i + 1}`] = item;
                });
                if (props.id) {
                  await newOrUpdateApplication_02(store.user.id, params);
                  ElMessage({
                    type: 'success',
                    message: '修改成功',
                  });
                } else {
                  const { data } = await newOrUpdateApplication_1(store.user.id, params);
                  ElMessage({
                    type: 'success',
                    message: '新增成功',
                  });
                  router.push({ name: 'PersonalProjectEdit', query: { id: data?.id } });
                }
              } catch (error) {
                console.log(error);
              } finally {
                saveLoading.value = false;
              }
            }
          },
        });
      }
    });
  };
  //保存草稿
  const onSaveTemp = () => {};

  const totalNum = ref(0);
  const tableTable = ref(0);
  const snpNum = ref(0);
  const dataNum = ref(0);
  const batchFieldNum = ref(0);
  const batchNum = ref(0);
  const hesNum = ref(0);
  const orderTable = ref([{ order: 3999999, tableData: [{ fieldId: '214545', name: 'xx症状' }] }]);
  //创建数据订单
  const onAddOrder = () => {
    const length = orderTable.value.length;
    const lastOrder = orderTable.value[length - 1]?.order || 3999999;
    orderTable.value.push({ order: Number(lastOrder) + 1, tableData: [] });
  };

  //在线帮助
  const onHelp = () => {
    // TODO
  };

  if (props.id) {
    fetchData();
  }
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findAppById(+props.id!, {} as any);
      // readonly.value = ['待审批', '审核通过'].includes(data?.state || '');
      Object.assign(appForm, data);
      let keywords: string[] = [];
      for (let i = 1; i <= 4; i++) {
        let value = data![`keyWord${i}`];
        if (value) {
          keywords.push(value);
        }
      }
      appForm.keyword = keywords.join(',');
      emit('getState', data?.state || '');
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="scss" scoped>
  .content-item {
    background-color: #fff;
    border-radius: 3px;
    display: flex;
    flex-direction: column;
    margin: 20px;
    margin-bottom: 0;

    .item-title {
      display: flex;
      justify-content: space-between;
      padding-top: 18px;
      padding-left: 28px;
      padding-right: 40px;
    }
  }
</style>
